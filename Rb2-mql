#include <Trade\Trade.mqh>

input double lote = 0.50;
input int stopLoss = 10;      // pips
input int takeProfit = 20;    // pips
input int trailingStart = 2;  // pips - quando começar o trailing
input int trailingStep = 5;   // pips - distância do trailing stop
input int maxOrdens = 5;      // número máximo de ordens permitidas

CTrade trade;
string prefixoComentario = "Manual_";  // Prefixo para identificar ordens deste EA

string botaoCompra = "btn_buy";
string botaoVenda = "btn_sell";
string botaoFechar = "btn_close_all"; // Novo botão para fechar todas as posições
string painelInfo = "painel_info";
string painelFundo = "painel_fundo";

// Adicionar variáveis para os objetos de linha
string prefixoLinhaTP = "tp_line_";
string prefixoLinhaSL = "sl_line_";
string prefixoLinhaEntrada = "entry_line_";

//+------------------------------------------------------------------+
int OnInit()
{
   CriarBotoes();
   CriarPainelInfo();
   ChartRedraw();
   Print("Script de Trading iniciado");
   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   ObjectDelete(0, botaoCompra);
   ObjectDelete(0, botaoVenda);
   ObjectDelete(0, botaoFechar); // Remover o botão de fechar
   ObjectDelete(0, painelInfo);
   ObjectDelete(0, painelFundo);
   
   // Remover todas as linhas de SL, TP e entrada
   RemoverTodasLinhasSLTP();
   
   ChartRedraw();
}

//+------------------------------------------------------------------+
void OnTick()
{
   // Verificar se alguma ordem atingiu o take profit ou stop loss
   VerificarFechamentoOrdens();
   
   // Gerenciar trailing stop para todas as posições abertas
   GerenciarTrailingStopTodasPosicoes();
   
   // Atualizar linhas de SL, TP e entrada no gráfico
   AtualizarLinhasSLTP();
   
   AtualizarPainelInfo();
}

//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
   if(id == CHARTEVENT_OBJECT_CLICK)
   {
      if(sparam == botaoCompra)
      {
         // Verificar se já atingiu o número máximo de ordens
         if(ContarOrdens() < maxOrdens)
            ExecutarCompra();
         else
            Print("Número máximo de ordens atingido: ", maxOrdens);
      }
      else if(sparam == botaoVenda)
      {
         // Verificar se já atingiu o número máximo de ordens
         if(ContarOrdens() < maxOrdens)
            ExecutarVenda();
         else
            Print("Número máximo de ordens atingido: ", maxOrdens);
      }
      else if(sparam == botaoFechar)
      {
         // Fechar todas as posições abertas
         FecharTodasPosicoes();
      }
   }
}

//+------------------------------------------------------------------+
int ContarOrdens()
{
   int contador = 0;
   
   // Contar posições abertas
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         // Verificar se a posição foi aberta por este EA
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
            contador++;
      }
   }
   
   return contador;
}

//+------------------------------------------------------------------+
void ExecutarCompra()
{
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
   
   double pipValue = point;
   if(digits == 5 || digits == 3)
      pipValue = point * 10;
   
   double sl = ask - (stopLoss * pipValue);
   double tp = ask + (takeProfit * pipValue);
   
   string comentario = prefixoComentario + "Compra_" + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS);
   
   Print("Executando COMPRA:");
   Print("Ask: ", ask, " SL: ", sl, " TP: ", tp);
   
   if(trade.Buy(lote, _Symbol, ask, sl, tp, comentario))
   {
      Print("✓ COMPRA executada com sucesso! Ticket: ", trade.ResultOrder());
      // Atualizar linhas de SL, TP e entrada imediatamente após abrir a posição
      AtualizarLinhasSLTP();
   }
   else
   {
      Print("✗ Erro na COMPRA: ", trade.ResultComment());
   }
}

//+------------------------------------------------------------------+
void ExecutarVenda()
{
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
   
   double pipValue = point;
   if(digits == 5 || digits == 3)
      pipValue = point * 10;
   
   double sl = bid + (stopLoss * pipValue);
   double tp = bid - (takeProfit * pipValue);
   
   string comentario = prefixoComentario + "Venda_" + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS);
   
   Print("Executando VENDA:");
   Print("Bid: ", bid, " SL: ", sl, " TP: ", tp);
   
   if(trade.Sell(lote, _Symbol, bid, sl, tp, comentario))
   {
      Print("✓ VENDA executada com sucesso! Ticket: ", trade.ResultOrder());
      // Atualizar linhas de SL, TP e entrada imediatamente após abrir a posição
      AtualizarLinhasSLTP();
   }
   else
   {
      Print("✗ Erro na VENDA: ", trade.ResultComment());
   }
}

//+------------------------------------------------------------------+
void GerenciarTrailingStopTodasPosicoes()
{
   int total = PositionsTotal();
   
   for(int i = 0; i < total; i++)
   {
      // Selecionar posição pelo índice
      string simbolo = PositionGetSymbol(i);
      
      // Verificar se é uma posição no símbolo atual
      if(simbolo == _Symbol)
      {
         // Verificar se a posição foi aberta por este EA
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            // Aplicar trailing stop para esta posição
            GerenciarTrailingStopPosicao(PositionGetInteger(POSITION_TICKET));
         }
      }
   }
}

//+------------------------------------------------------------------+
void GerenciarTrailingStopPosicao(ulong ticket)
{
   // Selecionar a posição pelo ticket
   if(!PositionSelectByTicket(ticket)) return;
   
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
   
   double pipValue = point;
   if(digits == 5 || digits == 3)
      pipValue = point * 10;
   
   double precoAtual = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? 
                       SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                       SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   
   double precoAbertura = PositionGetDouble(POSITION_PRICE_OPEN);
   double stopAtual = PositionGetDouble(POSITION_SL);
   double lucroEmPips = 0;
   double novoStop = 0;
   
   // Verificar se o stop loss está definido
   bool stopDefinido = (stopAtual > 0);
   
   if((ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
   {
      // Posição de COMPRA
      lucroEmPips = (precoAtual - precoAbertura) / pipValue;
      
      // Verificar se o lucro atingiu o nível para ativar o trailing (2 pips)
      if(lucroEmPips >= trailingStart)
      {
         // Calcular novo stop loss - mais próximo do preço atual para proteger o lucro
         novoStop = precoAtual - (trailingStep * pipValue);
         
         // Só move o stop se for melhor que o atual ou se não houver stop definido
         if(!stopDefinido || novoStop > stopAtual + (pipValue * 0.1))
         {
            if(trade.PositionModify(ticket, novoStop, PositionGetDouble(POSITION_TP)))
            {
               Print("Proteção de Lucro ativada - Ticket #", ticket, ": Novo SL = ", novoStop, " (Lucro: ", DoubleToString(lucroEmPips, 1), " pips)");
               // Atualizar linhas de SL, TP e entrada após modificar o stop loss
               AtualizarLinhasSLTP();
               
               // Adicionar alerta quando o trailing stop for ativado
               Alert("Trailing Stop ativado para posição #", ticket, " - Novo SL: ", DoubleToString(novoStop, _Digits));
            }
            else
            {
               Print("Erro ao atualizar Proteção de Lucro para ticket #", ticket, ": ", GetLastError());
            }
         }
      }
   }
   else
   {
      // Posição de VENDA
      lucroEmPips = (precoAbertura - precoAtual) / pipValue;
      
      // Verificar se o lucro atingiu o nível para ativar o trailing (2 pips)
      if(lucroEmPips >= trailingStart)
      {
         // Calcular novo stop loss - mais próximo do preço atual para proteger o lucro
         novoStop = precoAtual + (trailingStep * pipValue);
         
         // Só move o stop se for melhor que o atual ou se não houver stop definido
         if(!stopDefinido || novoStop < stopAtual - (pipValue * 0.1))
         {
            if(trade.PositionModify(ticket, novoStop, PositionGetDouble(POSITION_TP)))
            {
               Print("Proteção de Lucro ativada - Ticket #", ticket, ": Novo SL = ", novoStop, " (Lucro: ", DoubleToString(lucroEmPips, 1), " pips)");
               // Atualizar linhas de SL, TP e entrada após modificar o stop loss
               AtualizarLinhasSLTP();
               
               // Adicionar alerta quando o trailing stop for ativado
               Alert("Trailing Stop ativado para posição #", ticket, " - Novo SL: ", DoubleToString(novoStop, _Digits));
            }
            else
            {
               Print("Erro ao atualizar Proteção de Lucro para ticket #", ticket, ": ", GetLastError());
            }
         }
      }
   }
   
   // Adicionar log para depuração
   if(lucroEmPips > 0)
   {
      Print("Debug - Posição: ", ((ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? "COMPRA" : "VENDA"),
            ", Lucro: ", DoubleToString(lucroEmPips, 1), " pips",
            ", Stop Atual: ", DoubleToString(stopAtual, _Digits),
            ", Preço Atual: ", DoubleToString(precoAtual, _Digits));
   }
}

//+------------------------------------------------------------------+
void VerificarFechamentoOrdens()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(PositionSelectByTicket(ticket))
            {
               double precoAtual = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? 
                                  SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                                  SymbolInfoDouble(_Symbol, SYMBOL_ASK);
               
               double stopLossAtual = PositionGetDouble(POSITION_SL);
               double precoAbertura = PositionGetDouble(POSITION_PRICE_OPEN);
               double lucroAtual = PositionGetDouble(POSITION_PROFIT);
               
               // Calcular lucro em pips
               double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
               int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
               double pipValue = point;
               if(digits == 5 || digits == 3) pipValue = point * 10;
               
               double lucroEmPips = 0;
               
               // Verificar se o lucro é maior que 3.00 para ativar proteção
               if(lucroAtual > 3.00)
               {
                  // Criar um identificador único para controle usando o ticket
                  static ulong ultimoTicketProtegido = 0;
                  static double ultimoLucroProtegido = 0;

                  // Só aplicar proteção se for um novo ticket ou se ainda não foi protegido
                  if(ultimoTicketProtegido != ticket)
                  {
                     string tipoOrdem = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? "COMPRA" : "VENDA";

                     // Calcular novo stop loss para proteção de lucro
                     double novoStopProtecao = 0;
                     double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
                     int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
                     double pipValue = point;
                     if(digits == 5 || digits == 3) pipValue = point * 10;

                     if((ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
                     {
                        // Para COMPRA: mover stop loss para breakeven + 1 pip de lucro
                        novoStopProtecao = precoAbertura + (1 * pipValue);
                     }
                     else
                     {
                        // Para VENDA: mover stop loss para breakeven + 1 pip de lucro
                        novoStopProtecao = precoAbertura - (1 * pipValue);
                     }

                     // Verificar se o novo stop é melhor que o atual
                     bool deveAtualizarStop = false;
                     if((ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
                     {
                        deveAtualizarStop = (stopLossAtual == 0 || novoStopProtecao > stopLossAtual);
                     }
                     else
                     {
                        deveAtualizarStop = (stopLossAtual == 0 || novoStopProtecao < stopLossAtual);
                     }

                     if(deveAtualizarStop)
                     {
                        // Aplicar a proteção de lucro
                        if(trade.PositionModify(ticket, novoStopProtecao, PositionGetDouble(POSITION_TP)))
                        {
                           string mensagem = "PROTEÇÃO ATIVADA: Posição #" + IntegerToString(ticket) +
                                           " (" + tipoOrdem + ") com LUCRO de $" +
                                           DoubleToString(lucroAtual, 2) +
                                           " - Stop movido para breakeven+1pip: " +
                                           DoubleToString(novoStopProtecao, _Digits);

                           // Enviar alerta
                           Alert(mensagem);
                           Print(mensagem);

                           // Atualizar linhas no gráfico
                           AtualizarLinhasSLTP();

                           // Marcar como protegido
                           ultimoTicketProtegido = ticket;
                           ultimoLucroProtegido = lucroAtual;
                        }
                        else
                        {
                           Print("Erro ao aplicar proteção de lucro para ticket #", ticket, ": ", GetLastError());
                        }
                     }
                  }
               }
               
               if((ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
               {
                  lucroEmPips = (precoAtual - precoAbertura) / pipValue;
                  
                  // Verificar se atingiu o stop loss
                  if(stopLossAtual > 0 && precoAtual <= stopLossAtual)
                  {
                     if(trade.PositionClose(ticket))
                     {
                        Print("✓ Posição fechada por Stop Loss - COMPRA, Ticket #", ticket, 
                              ", Perda: ", DoubleToString(-lucroEmPips, 1), " pips");
                     }
                     else
                     {
                        Print("✗ Erro ao fechar posição por Stop Loss: ", GetLastError());
                     }
                  }
               }
               else // POSITION_TYPE_SELL
               {
                  lucroEmPips = (precoAbertura - precoAtual) / pipValue;
                  
                  // Verificar se atingiu o stop loss
                  if(stopLossAtual > 0 && precoAtual >= stopLossAtual)
                  {
                     if(trade.PositionClose(ticket))
                     {
                        Print("✓ Posição fechada por Stop Loss - VENDA, Ticket #", ticket, 
                              ", Perda: ", DoubleToString(-lucroEmPips, 1), " pips");
                     }
                     else
                     {
                        Print("✗ Erro ao fechar posição por Stop Loss: ", GetLastError());
                     }
                  }
               }
               
               // Verificar se atingiu o lucro desejado (por exemplo, 5 pips)
               if(lucroEmPips >= 5.0)
               {
                  Print("Posição com lucro de ", DoubleToString(lucroEmPips, 1), " pips - verificando proteção");
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
void CriarBotoes()
{
   // Botão COMPRA
   ObjectDelete(0, botaoCompra);
   if(ObjectCreate(0, botaoCompra, OBJ_RECTANGLE_LABEL, 0, 0, 0))
   {
      ObjectSetInteger(0, botaoCompra, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
      ObjectSetInteger(0, botaoCompra, OBJPROP_XDISTANCE, 200);
      ObjectSetInteger(0, botaoCompra, OBJPROP_YDISTANCE, 50);
      ObjectSetInteger(0, botaoCompra, OBJPROP_XSIZE, 80);
      ObjectSetInteger(0, botaoCompra, OBJPROP_YSIZE, 30);
      ObjectSetInteger(0, botaoCompra, OBJPROP_BGCOLOR, clrGreen);
      ObjectSetInteger(0, botaoCompra, OBJPROP_BORDER_COLOR, clrWhite);
      ObjectSetInteger(0, botaoCompra, OBJPROP_COLOR, clrWhite);
      ObjectSetString(0, botaoCompra, OBJPROP_TEXT, "COMPRA");
      ObjectSetInteger(0, botaoCompra, OBJPROP_FONTSIZE, 10);
      ObjectSetInteger(0, botaoCompra, OBJPROP_SELECTABLE, true);
   }
   
   // Botão VENDA
   ObjectDelete(0, botaoVenda);
   if(ObjectCreate(0, botaoVenda, OBJ_RECTANGLE_LABEL, 0, 0, 0))
   {
      ObjectSetInteger(0, botaoVenda, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
      ObjectSetInteger(0, botaoVenda, OBJPROP_XDISTANCE, 110);
      ObjectSetInteger(0, botaoVenda, OBJPROP_YDISTANCE, 50);
      ObjectSetInteger(0, botaoVenda, OBJPROP_XSIZE, 80);
      ObjectSetInteger(0, botaoVenda, OBJPROP_YSIZE, 30);
      ObjectSetInteger(0, botaoVenda, OBJPROP_BGCOLOR, clrRed);
      ObjectSetInteger(0, botaoVenda, OBJPROP_BORDER_COLOR, clrWhite);
      ObjectSetInteger(0, botaoVenda, OBJPROP_COLOR, clrWhite);
      ObjectSetString(0, botaoVenda, OBJPROP_TEXT, "VENDA");
      ObjectSetInteger(0, botaoVenda, OBJPROP_FONTSIZE, 10);
      ObjectSetInteger(0, botaoVenda, OBJPROP_SELECTABLE, true);
   }
   
   // Botão FECHAR TUDO
   ObjectDelete(0, botaoFechar);
   if(ObjectCreate(0, botaoFechar, OBJ_RECTANGLE_LABEL, 0, 0, 0))
   {
      ObjectSetInteger(0, botaoFechar, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
      ObjectSetInteger(0, botaoFechar, OBJPROP_XDISTANCE, 155);
      ObjectSetInteger(0, botaoFechar, OBJPROP_YDISTANCE, 90);
      ObjectSetInteger(0, botaoFechar, OBJPROP_XSIZE, 125);
      ObjectSetInteger(0, botaoFechar, OBJPROP_YSIZE, 30);
      ObjectSetInteger(0, botaoFechar, OBJPROP_BGCOLOR, clrDarkOrange);
      ObjectSetInteger(0, botaoFechar, OBJPROP_BORDER_COLOR, clrWhite);
      ObjectSetInteger(0, botaoFechar, OBJPROP_COLOR, clrWhite);
      ObjectSetString(0, botaoFechar, OBJPROP_TEXT, "FECHAR TUDO");
      ObjectSetInteger(0, botaoFechar, OBJPROP_FONTSIZE, 10);
      ObjectSetInteger(0, botaoFechar, OBJPROP_SELECTABLE, true);
   }
}

//+------------------------------------------------------------------+
void CriarPainelInfo()
{
   // Fundo do painel
   ObjectDelete(0, painelFundo);
   if(ObjectCreate(0, painelFundo, OBJ_RECTANGLE_LABEL, 0, 0, 0))
   {
      ObjectSetInteger(0, painelFundo, OBJPROP_CORNER, CORNER_LEFT_LOWER);
      ObjectSetInteger(0, painelFundo, OBJPROP_XDISTANCE, 10);
      ObjectSetInteger(0, painelFundo, OBJPROP_YDISTANCE, 10);
      ObjectSetInteger(0, painelFundo, OBJPROP_XSIZE, 280);  // Aumentado para acomodar o texto
      ObjectSetInteger(0, painelFundo, OBJPROP_YSIZE, 150);  // Aumentado para acomodar o texto
      ObjectSetInteger(0, painelFundo, OBJPROP_BGCOLOR, clrBlack);
      ObjectSetInteger(0, painelFundo, OBJPROP_BORDER_COLOR, clrWhite);
   }
   
   // Texto do painel
   ObjectDelete(0, painelInfo);
   if(ObjectCreate(0, painelInfo, OBJ_LABEL, 0, 0, 0))
   {
      ObjectSetInteger(0, painelInfo, OBJPROP_CORNER, CORNER_LEFT_LOWER);
      ObjectSetInteger(0, painelInfo, OBJPROP_XDISTANCE, 20);
      ObjectSetInteger(0, painelInfo, OBJPROP_YDISTANCE, 130);  // Ajustado para centralizar no painel
      ObjectSetInteger(0, painelInfo, OBJPROP_COLOR, clrLimeGreen);
      ObjectSetString(0, painelInfo, OBJPROP_FONT, "Consolas");
      ObjectSetInteger(0, painelInfo, OBJPROP_FONTSIZE, 10);
      ObjectSetInteger(0, painelInfo, OBJPROP_ANCHOR, ANCHOR_LEFT_UPPER);
   }
}

//+------------------------------------------------------------------+
void AtualizarPainelInfo()
{
   int totalOrdens = ContarOrdens();
   double lucroTotal = 0;
   double pipsTotal = 0;
   
   // Calcular lucro total e pips de todas as posições
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            lucroTotal += PositionGetDouble(POSITION_PROFIT);
            
            double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
            int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
            double pipValue = point;
            if(digits == 5 || digits == 3) pipValue = point * 10;
            
            double precoAtual = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? 
                               SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                               SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            double precoAbertura = PositionGetDouble(POSITION_PRICE_OPEN);
            
            double lucroEmPips = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? 
                                (precoAtual - precoAbertura) / pipValue :
                                (precoAbertura - precoAtual) / pipValue;
            
            pipsTotal += lucroEmPips;
         }
      }
   }
   
   string status = (totalOrdens > 0) ? IntegerToString(totalOrdens) + " Ordens Abertas" : "Sem Ordens";
   
   // Texto formatado com caracteres de caixa Unicode para melhor aparência
   string texto = "┌─────────────────────────┐\n" +
                  "│   TRADING MANUAL        │\n" +
                  "│ Status: " + StringFormat("%-15s", status) + "│\n" +
                  "│ Ordens: " + StringFormat("%-15d", totalOrdens) + "│\n" +
                  "│ Lucro:  $" + StringFormat("%-14s", DoubleToString(lucroTotal, 2)) + "│\n" +
                  "│ Pips:   " + StringFormat("%-15s", DoubleToString(pipsTotal, 1)) + "│\n" +
                  "│ Lote:   " + StringFormat("%-15.2f", lote) + "│\n" +
                  "│ SL: " + IntegerToString(stopLoss) + " | TP: " + IntegerToString(takeProfit) + " pips    │\n" +
                  "└─────────────────────────┘";
   
   ObjectSetString(0, painelInfo, OBJPROP_TEXT, texto);
}

//+------------------------------------------------------------------+
void RemoverTodasLinhasSLTP()
{
   // Remover todas as linhas de SL, TP e entrada
   for(int i = ObjectsTotal(0, 0, OBJ_HLINE) - 1; i >= 0; i--)
   {
      string nome = ObjectName(0, i, 0, OBJ_HLINE);
      if(StringFind(nome, prefixoLinhaSL) == 0 || 
         StringFind(nome, prefixoLinhaTP) == 0 || 
         StringFind(nome, prefixoLinhaEntrada) == 0)
      {
         ObjectDelete(0, nome);
      }
   }
}

//+------------------------------------------------------------------+
void AtualizarLinhasSLTP()
{
   // Primeiro, remover todas as linhas existentes
   RemoverTodasLinhasSLTP();
   
   // Depois, criar novas linhas para cada posição aberta
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(PositionSelectByTicket(ticket))
            {
               double stopLoss = PositionGetDouble(POSITION_SL);
               double takeProfit = PositionGetDouble(POSITION_TP);
               double precoEntrada = PositionGetDouble(POSITION_PRICE_OPEN);
               ENUM_POSITION_TYPE tipo = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
               
               // Criar linha de preço de entrada
               string nomeLinhaEntrada = prefixoLinhaEntrada + IntegerToString(ticket);
               if(ObjectCreate(0, nomeLinhaEntrada, OBJ_HLINE, 0, 0, precoEntrada))
               {
                  ObjectSetInteger(0, nomeLinhaEntrada, OBJPROP_COLOR, clrYellow);
                  ObjectSetInteger(0, nomeLinhaEntrada, OBJPROP_STYLE, STYLE_SOLID);
                  ObjectSetInteger(0, nomeLinhaEntrada, OBJPROP_WIDTH, 1);
                  ObjectSetString(0, nomeLinhaEntrada, OBJPROP_TOOLTIP, "Entrada #" + IntegerToString(ticket) + 
                                 " (" + (tipo == POSITION_TYPE_BUY ? "COMPRA" : "VENDA") + ")");
               }
               
               // Criar linha de Stop Loss
               if(stopLoss > 0)
               {
                  string nomeLinhaSL = prefixoLinhaSL + IntegerToString(ticket);
                  if(ObjectCreate(0, nomeLinhaSL, OBJ_HLINE, 0, 0, stopLoss))
                  {
                     ObjectSetInteger(0, nomeLinhaSL, OBJPROP_COLOR, clrRed);
                     ObjectSetInteger(0, nomeLinhaSL, OBJPROP_STYLE, STYLE_DASH);
                     ObjectSetInteger(0, nomeLinhaSL, OBJPROP_WIDTH, 1);
                     ObjectSetString(0, nomeLinhaSL, OBJPROP_TOOLTIP, "Stop Loss #" + IntegerToString(ticket) + 
                                    " (" + (tipo == POSITION_TYPE_BUY ? "COMPRA" : "VENDA") + ")");
                  }
               }
               
               // Criar linha de Take Profit
               if(takeProfit > 0)
               {
                  string nomeLinhaTP = prefixoLinhaTP + IntegerToString(ticket);
                  if(ObjectCreate(0, nomeLinhaTP, OBJ_HLINE, 0, 0, takeProfit))
                  {
                     ObjectSetInteger(0, nomeLinhaTP, OBJPROP_COLOR, clrGreen);
                     ObjectSetInteger(0, nomeLinhaTP, OBJPROP_STYLE, STYLE_DASH);
                     ObjectSetInteger(0, nomeLinhaTP, OBJPROP_WIDTH, 1);
                     ObjectSetString(0, nomeLinhaTP, OBJPROP_TOOLTIP, "Take Profit #" + IntegerToString(ticket) + 
                                    " (" + (tipo == POSITION_TYPE_BUY ? "COMPRA" : "VENDA") + ")");
                  }
               }
            }
         }
      }
   }
   
   ChartRedraw();
}

//+------------------------------------------------------------------+
void FecharTodasPosicoes()
{
   int total = PositionsTotal();
   
   // Precisamos iterar de trás para frente porque o PositionsTotal() muda quando fechamos posições
   for(int i = total - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(trade.PositionClose(ticket))
            {
               Print("✓ Posição fechada manualmente - Ticket #", ticket);
            }
            else
            {
               Print("✗ Erro ao fechar posição: ", GetLastError());
            }
         }
      }
   }
   
   // Atualizar o gráfico após fechar todas as posições
   AtualizarLinhasSLTP();
   AtualizarPainelInfo();
}
