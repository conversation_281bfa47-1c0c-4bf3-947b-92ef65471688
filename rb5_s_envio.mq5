#include <Trade\Trade.mqh>

// Renomear as variáveis de input para evitar conflitos
input double lote = 0.01;
input int inputStopLoss = 5;      // pips
input int inputTakeProfit = 5;    // pips
input int trailingStart = 2;  // pips - quando começar o trailing
input int trailingStep = 5;   // pips - distância do trailing stop
input int maxOrdens = 5;      // número máximo de ordens permitidas
input double lucroProtecao = 3.00; // valor em $ para ativar proteção de lucro
input double lucroFechamentoAutomatico = 8.00; // valor em $ para fechamento automático

CTrade trade;
string prefixoComentario = "RoboSMT5_";  // Prefixo para identificar ordens deste EA

string botaoCompra = "btn_buy";
string botaoVenda = "btn_sell";
string botaoFechar = "btn_close_all";
string textoInfo = "info_text";
string painelFundo = "painel_fundo";

// Variáveis para os objetos de linha
string prefixoLinhaTP = "tp_line_";
string prefixoLinhaSL = "sl_line_";
string prefixoLinhaEntrada = "entry_line_";

// Variáveis de controle para proteção de lucro
struct ProtecaoLucro
{
   ulong ticket;
   bool protegido;
   double lucroQuandoProtegido;
};

ProtecaoLucro posicoes_protegidas[100]; // Array para controlar posições protegidas
int total_protegidas = 0;

// Variáveis para controle de alertas de lucro
struct AlertaLucro
{
   ulong ticket;
   double ultimoLucroAlertado;
   datetime ultimoAlerta;
};

AlertaLucro alertas_lucro[100]; // Array para controlar alertas de lucro
int total_alertas = 0;

// Variáveis para o novo texto de informações
int greens = 0;  // Contador de operações com lucro
int reds = 0;    // Contador de operações com prejuízo
double lucroTotal = 0.0;  // Lucro total do dia

//+------------------------------------------------------------------+
int OnInit()
{
   // Inicializar array de proteção
   for(int i = 0; i < 100; i++)
   {
      posicoes_protegidas[i].ticket = 0;
      posicoes_protegidas[i].protegido = false;
      posicoes_protegidas[i].lucroQuandoProtegido = 0.0;
   }
   total_protegidas = 0;
   
   // Inicializar array de alertas
   for(int i = 0; i < 100; i++)
   {
      alertas_lucro[i].ticket = 0;
      alertas_lucro[i].ultimoLucroAlertado = 0.0;
      alertas_lucro[i].ultimoAlerta = 0;
   }
   total_alertas = 0;
   
   // Resetar contadores de operações
   greens = 0;
   reds = 0;
   lucroTotal = 0.0;

   CriarBotoes();
   CriarTextoInfo();
   ChartRedraw();

   Print("=== ROBO SMT5 INICIADO ===");
   Print("Configurações:");
   Print("- Lote: ", lote);
   Print("- Stop Loss: ", inputStopLoss, " pips");
   Print("- Take Profit: ", inputTakeProfit, " pips");
   Print("- Proteção de Lucro: $", lucroProtecao);
   Print("- Fechamento Automático: $", lucroFechamentoAutomatico);
   Print("- Trailing Start: ", trailingStart, " pips");
   Print("- Max Ordens: ", maxOrdens);
   Print("========================");

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
void OnDeinit(const int reason)
{
   ObjectDelete(0, botaoCompra);
   ObjectDelete(0, botaoVenda);
   ObjectDelete(0, botaoFechar);
   ObjectDelete(0, textoInfo);
   
   // Remover todas as linhas de SL, TP e entrada
   RemoverTodasLinhasSLTP();
   
   ChartRedraw();
}

//+------------------------------------------------------------------+
void OnTick()
{
   // Limpar posições protegidas que já foram fechadas
   LimparPosicoesProtegidas();
   
   // Limpar alertas de lucro para posições fechadas
   LimparAlertasLucro();

   // Verificar fechamento automático por lucro
   VerificarFechamentoAutomatico();

   // Verificar proteção de lucro primeiro (mais importante)
   VerificarProtecaoLucro();
   
   // Verificar alertas de lucro
   VerificarAlertasLucro();

   // Verificar se alguma ordem atingiu o take profit ou stop loss
   VerificarFechamentoOrdens();

   // Gerenciar trailing stop para todas as posições abertas
   GerenciarTrailingStopTodasPosicoes();

   // Atualizar linhas de SL, TP e entrada no gráfico
   AtualizarLinhasSLTP();

   // Atualizar texto de informações
   AtualizarTextoInfo();
}

//+------------------------------------------------------------------+
void OnChartEvent(const int id, const long &lparam, const double &dparam, const string &sparam)
{
   if(id == CHARTEVENT_OBJECT_CLICK)
   {
      if(sparam == botaoCompra)
      {
         // Verificar se já atingiu o número máximo de ordens
         if(ContarOrdens() < maxOrdens)
            ExecutarCompra();
         else
            Print("Número máximo de ordens atingido: ", maxOrdens);
      }
      else if(sparam == botaoVenda)
      {
         // Verificar se já atingiu o número máximo de ordens
         if(ContarOrdens() < maxOrdens)
            ExecutarVenda();
         else
            Print("Número máximo de ordens atingido: ", maxOrdens);
      }
      else if(sparam == botaoFechar)
      {
         // Fechar todas as posições abertas
         FecharTodasPosicoes();
      }
   }
}

//+------------------------------------------------------------------+
int ContarOrdens()
{
   int contador = 0;
   
   // Contar posições abertas
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         // Verificar se a posição foi aberta por este EA
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
            contador++;
      }
   }
   
   return contador;
}

//+------------------------------------------------------------------+
void ExecutarCompra()
{
   double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
   
   double pipValue = point;
   if(digits == 5 || digits == 3)
      pipValue = point * 10;
   
   double sl = ask - (inputStopLoss * pipValue);
   double tp = ask + (inputTakeProfit * pipValue);
   
   string comentario = prefixoComentario + "Compra_" + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS);
   
   Print("Executando COMPRA:");
   Print("Ask: ", ask, " SL: ", sl, " TP: ", tp);
   
   if(trade.Buy(lote, _Symbol, ask, sl, tp, comentario))
   {
      Print("✓ COMPRA executada com sucesso! Ticket: ", trade.ResultOrder());
      // Atualizar linhas de SL, TP e entrada imediatamente após abrir a posição
      AtualizarLinhasSLTP();
   }
   else
   {
      Print("✗ Erro na COMPRA: ", trade.ResultComment());
   }
}

//+------------------------------------------------------------------+
void ExecutarVenda()
{
   double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
   
   double pipValue = point;
   if(digits == 5 || digits == 3)
      pipValue = point * 10;
   
   double sl = bid + (inputStopLoss * pipValue);
   double tp = bid - (inputTakeProfit * pipValue);
   
   string comentario = prefixoComentario + "Venda_" + TimeToString(TimeCurrent(), TIME_DATE|TIME_SECONDS);
   
   Print("Executando VENDA:");
   Print("Bid: ", bid, " SL: ", sl, " TP: ", tp);
   
   if(trade.Sell(lote, _Symbol, bid, sl, tp, comentario))
   {
      Print("✓ VENDA executada com sucesso! Ticket: ", trade.ResultOrder());
      // Atualizar linhas de SL, TP e entrada imediatamente após abrir a posição
      AtualizarLinhasSLTP();
   }
   else
   {
      Print("✗ Erro na VENDA: ", trade.ResultComment());
   }
}

//+------------------------------------------------------------------+
void GerenciarTrailingStopTodasPosicoes()
{
   int total = PositionsTotal();
   
   for(int i = 0; i < total; i++)
   {
      // Selecionar posição pelo índice
      string simbolo = PositionGetSymbol(i);
      
      // Verificar se é uma posição no símbolo atual
      if(simbolo == _Symbol)
      {
         // Verificar se a posição foi aberta por este EA
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            // Aplicar trailing stop para esta posição
            GerenciarTrailingStopPosicao(PositionGetInteger(POSITION_TICKET));
         }
      }
   }
}

//+------------------------------------------------------------------+
void GerenciarTrailingStopPosicao(ulong ticket)
{
   // Selecionar a posição pelo ticket
   if(!PositionSelectByTicket(ticket)) return;
   
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
   
   double pipValue = point;
   if(digits == 5 || digits == 3)
      pipValue = point * 10;
   
   double precoAtual = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? 
                       SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                       SymbolInfoDouble(_Symbol, SYMBOL_ASK);
   
   double precoAbertura = PositionGetDouble(POSITION_PRICE_OPEN);
   double stopAtual = PositionGetDouble(POSITION_SL);
   double lucroAtual = PositionGetDouble(POSITION_PROFIT);
   
   // Verificar se o stop loss está definido
   bool stopDefinido = (stopAtual > 0);
   
   // Verificar se a posição está protegida
   bool posicaoProtegida = false;
   double lucroQuandoProtegido = 0;
   for(int i = 0; i < total_protegidas; i++)
   {
      if(posicoes_protegidas[i].ticket == ticket && posicoes_protegidas[i].protegido)
      {
         posicaoProtegida = true;
         lucroQuandoProtegido = posicoes_protegidas[i].lucroQuandoProtegido;
         break;
      }
   }
   
   // Se a posição já está protegida, aplicar trailing stop mais agressivo
   if(posicaoProtegida)
   {
      double novoStop = 0;
      
      if((ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
      {
         // Posição de COMPRA
         // Calcular novo stop loss - mais próximo do preço atual para proteger o lucro
         novoStop = precoAtual - (trailingStep * pipValue);
         
         // Só move o stop se for melhor que o atual
         if(stopAtual == 0 || novoStop > stopAtual + (pipValue * 0.1))
         {
            if(trade.PositionModify(ticket, novoStop, PositionGetDouble(POSITION_TP)))
            {
               Print("Trailing Stop atualizado - Ticket #", ticket, 
                     ": Novo SL = ", novoStop, 
                     " (Lucro atual: $", DoubleToString(lucroAtual, 2), 
                     ", Lucro quando protegido: $", DoubleToString(lucroQuandoProtegido, 2), ")");
               
               // Atualizar linhas de SL, TP e entrada após modificar o stop loss
               AtualizarLinhasSLTP();
            }
         }
      }
      else
      {
         // Posição de VENDA
         // Calcular novo stop loss - mais próximo do preço atual para proteger o lucro
         novoStop = precoAtual + (trailingStep * pipValue);
         
         // Só move o stop se for melhor que o atual
         if(stopAtual == 0 || novoStop < stopAtual - (pipValue * 0.1))
         {
            if(trade.PositionModify(ticket, novoStop, PositionGetDouble(POSITION_TP)))
            {
               Print("Trailing Stop atualizado - Ticket #", ticket, 
                     ": Novo SL = ", novoStop, 
                     " (Lucro atual: $", DoubleToString(lucroAtual, 2), 
                     ", Lucro quando protegido: $", DoubleToString(lucroQuandoProtegido, 2), ")");
               
               // Atualizar linhas de SL, TP e entrada após modificar o stop loss
               AtualizarLinhasSLTP();
            }
         }
      }
   }
   // Se não está protegida e o lucro atingiu o valor de proteção, ativar proteção
   else if(lucroAtual >= lucroProtecao)
   {
      AplicarProtecaoLucro(ticket, lucroAtual);
   }
   // Se não está protegida, aplicar o trailing normal quando atingir trailingStart pips
   else
   {
      if((ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
      {
         // Posição de COMPRA
         double lucroEmPips = (precoAtual - precoAbertura) / pipValue;
         
         // Verificar se o lucro atingiu o nível para ativar o trailing (trailingStart pips)
         if(lucroEmPips >= trailingStart)
         {
            // Calcular novo stop loss - mais próximo do preço atual para proteger o lucro
            double novoStop = precoAtual - (trailingStep * pipValue);
            
            // Só move o stop se for melhor que o atual ou se não houver stop definido
            if(!stopDefinido || novoStop > stopAtual + (pipValue * 0.1))
            {
               if(trade.PositionModify(ticket, novoStop, PositionGetDouble(POSITION_TP)))
               {
                  Print("Trailing Stop ativado - Ticket #", ticket, ": Novo SL = ", novoStop, " (Lucro: ", DoubleToString(lucroEmPips, 1), " pips)");
                  // Atualizar linhas de SL, TP e entrada após modificar o stop loss
                  AtualizarLinhasSLTP();
               }
            }
         }
      }
      else
      {
         // Posição de VENDA
         double lucroEmPips = (precoAbertura - precoAtual) / pipValue;
         
         // Verificar se o lucro atingiu o nível para ativar o trailing (trailingStart pips)
         if(lucroEmPips >= trailingStart)
         {
            // Calcular novo stop loss - mais próximo do preço atual para proteger o lucro
            double novoStop = precoAtual + (trailingStep * pipValue);
            
            // Só move o stop se for melhor que o atual ou se não houver stop definido
            if(!stopDefinido || novoStop < stopAtual - (pipValue * 0.1))
            {
               if(trade.PositionModify(ticket, novoStop, PositionGetDouble(POSITION_TP)))
               {
                  Print("Trailing Stop ativado - Ticket #", ticket, ": Novo SL = ", novoStop, " (Lucro: ", DoubleToString(lucroEmPips, 1), " pips)");
                  // Atualizar linhas de SL, TP e entrada após modificar o stop loss
                  AtualizarLinhasSLTP();
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
void VerificarFechamentoOrdens()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(PositionSelectByTicket(ticket))
            {
               double precoAtual = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? 
                                  SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                                  SymbolInfoDouble(_Symbol, SYMBOL_ASK);
               
               double stopLossAtual = PositionGetDouble(POSITION_SL);
               double precoAbertura = PositionGetDouble(POSITION_PRICE_OPEN);
               double lucroAtual = PositionGetDouble(POSITION_PROFIT);
               
               // Calcular lucro em pips
               double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
               int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
               double pipValue = point;
               if(digits == 5 || digits == 3) pipValue = point * 10;
               
               double lucroEmPips = 0;
               
               // Verificar se a posição está protegida e se o lucro caiu significativamente
               for(int j = 0; j < total_protegidas; j++)
               {
                  if(posicoes_protegidas[j].ticket == ticket && posicoes_protegidas[j].protegido)
                  {
                     double lucroQuandoProtegido = posicoes_protegidas[j].lucroQuandoProtegido;
                     double lucroMaximo = lucroQuandoProtegido;
                     
                     // Se o lucro atual caiu para menos de 70% do lucro máximo registrado
                     if(lucroAtual < lucroMaximo * 0.7 && lucroAtual > 0)
                     {
                        string tipoOrdem = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? "COMPRA" : "VENDA";
                        string mensagem = "⚠️ FECHANDO POSIÇÃO PROTEGIDA! #" + IntegerToString(ticket) + 
                                         " (" + tipoOrdem + ") - Lucro caiu de $" + 
                                         DoubleToString(lucroMaximo, 2) + " para $" + 
                                         DoubleToString(lucroAtual, 2);
                        
                        Alert(mensagem);
                        Print(mensagem);
                        
                        if(trade.PositionClose(ticket))
                        {
                           Print("✓ Posição fechada por proteção de lucro - Ticket #", ticket);
                        }
                        else
                        {
                           Print("✗ Erro ao fechar posição por proteção: ", GetLastError());
                        }
                        
                        break;
                     }
                     
                     // Atualizar o lucro máximo registrado se o lucro atual for maior
                     if(lucroAtual > lucroMaximo)
                     {
                        posicoes_protegidas[j].lucroQuandoProtegido = lucroAtual;
                        Print("Lucro máximo atualizado para posição #", ticket, ": $", DoubleToString(lucroAtual, 2));
                     }
                  }
               }
               
               // Verificar se atingiu o stop loss
               if((ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY)
               {
                  lucroEmPips = (precoAtual - precoAbertura) / pipValue;
                  
                  // Verificar se atingiu o stop loss
                  if(stopLossAtual > 0 && precoAtual <= stopLossAtual)
                  {
                     if(trade.PositionClose(ticket))
                     {
                        Print("✓ Posição fechada por Stop Loss - COMPRA, Ticket #", ticket, 
                              ", Perda: ", DoubleToString(-lucroEmPips, 1), " pips");
                     }
                     else
                     {
                        Print("✗ Erro ao fechar posição por Stop Loss: ", GetLastError());
                     }
                  }
               }
               else // POSITION_TYPE_SELL
               {
                  lucroEmPips = (precoAbertura - precoAtual) / pipValue;
                  
                  // Verificar se atingiu o stop loss
                  if(stopLossAtual > 0 && precoAtual >= stopLossAtual)
                  {
                     if(trade.PositionClose(ticket))
                     {
                        Print("✓ Posição fechada por Stop Loss - VENDA, Ticket #", ticket, 
                              ", Perda: ", DoubleToString(-lucroEmPips, 1), " pips");
                     }
                     else
                     {
                        Print("✗ Erro ao fechar posição por Stop Loss: ", GetLastError());
                     }
                  }
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
void VerificarAlertasLucro()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            double lucroAtual = PositionGetDouble(POSITION_PROFIT);
            
            // Verificar se o lucro é positivo (maior que 0)
            if(lucroAtual > 0)
            {
               // Verificar se já temos um alerta para este ticket
               int indiceAlerta = -1;
               for(int j = 0; j < total_alertas; j++)
               {
                  if(alertas_lucro[j].ticket == ticket)
                  {
                     indiceAlerta = j;
                     break;
                  }
               }
               
               // Se não temos alerta ou o lucro aumentou significativamente
               if(indiceAlerta == -1 || lucroAtual >= alertas_lucro[indiceAlerta].ultimoLucroAlertado + 1.0)
               {
                  // Verificar se passaram pelo menos 30 segundos desde o último alerta
                  bool podeAlertar = true;
                  if(indiceAlerta != -1)
                  {
                     if(TimeCurrent() - alertas_lucro[indiceAlerta].ultimoAlerta < 30)
                     {
                        podeAlertar = false;
                     }
                  }
                  
                  if(podeAlertar)
                  {
                     string tipoOrdem = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? "COMPRA" : "VENDA";
                     string mensagem = "💰 ALERTA: Posição #" + IntegerToString(ticket) + 
                                      " (" + tipoOrdem + ") com LUCRO de $" + 
                                      DoubleToString(lucroAtual, 2);
                     
                     // Usar Alert para garantir que o usuário veja a notificação
                     Alert(mensagem);
                     Print(mensagem);
                     
                     // Atualizar ou adicionar ao array de alertas
                     if(indiceAlerta == -1)
                     {
                        // Adicionar novo alerta
                        if(total_alertas < 100)
                        {
                           alertas_lucro[total_alertas].ticket = ticket;
                           alertas_lucro[total_alertas].ultimoLucroAlertado = lucroAtual;
                           alertas_lucro[total_alertas].ultimoAlerta = TimeCurrent();
                           total_alertas++;
                        }
                     }
                     else
                     {
                        // Atualizar alerta existente
                        alertas_lucro[indiceAlerta].ultimoLucroAlertado = lucroAtual;
                        alertas_lucro[indiceAlerta].ultimoAlerta = TimeCurrent();
                     }
                  }
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
void LimparAlertasLucro()
{
   // Remover alertas para posições que já foram fechadas
   for(int i = total_alertas - 1; i >= 0; i--)
   {
      if(!PositionSelectByTicket(alertas_lucro[i].ticket))
      {
         // Posição foi fechada, remover do array
         for(int j = i; j < total_alertas - 1; j++)
         {
            alertas_lucro[j] = alertas_lucro[j + 1];
         }
         total_alertas--;
      }
   }
}

//+------------------------------------------------------------------+
void VerificarProtecaoLucro()
{
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            double lucroAtual = PositionGetDouble(POSITION_PROFIT);
            
            // Adicionar log para depuração
            if(lucroAtual > 1.0)
            {
               Print("DEBUG: Verificando proteção - Ticket #", ticket, 
                     ", Lucro: $", DoubleToString(lucroAtual, 2), 
                     ", Limite: $", DoubleToString(lucroProtecao, 2),
                     ", Já protegida: ", (PosicaoJaProtegida(ticket) ? "Sim" : "Não"));
            }

            // Verificar se precisa aplicar proteção
            if(lucroAtual >= lucroProtecao && !PosicaoJaProtegida(ticket))
            {
               string tipoOrdem = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? "COMPRA" : "VENDA";
               string mensagem = "🛡️ PROTEÇÃO: Posição #" + IntegerToString(ticket) + 
                               " (" + tipoOrdem + ") atingiu $" + 
                               DoubleToString(lucroAtual, 2) + " - Aplicando proteção...";
               
               Alert(mensagem);
               Print(mensagem);
               
               AplicarProtecaoLucro(ticket, lucroAtual);
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
bool PosicaoJaProtegida(ulong ticket)
{
   for(int i = 0; i < total_protegidas; i++)
   {
      if(posicoes_protegidas[i].ticket == ticket && posicoes_protegidas[i].protegido)
      {
         return true;
      }
   }
   return false;
}

//+------------------------------------------------------------------+
void AplicarProtecaoLucro(ulong ticket, double lucroAtual)
{
   if(!PositionSelectByTicket(ticket)) return;

   double precoAbertura = PositionGetDouble(POSITION_PRICE_OPEN);
   double stopAtual = PositionGetDouble(POSITION_SL);
   ENUM_POSITION_TYPE tipo = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
   double precoAtual = (tipo == POSITION_TYPE_BUY) ? 
                       SymbolInfoDouble(_Symbol, SYMBOL_BID) : 
                       SymbolInfoDouble(_Symbol, SYMBOL_ASK);

   // Calcular valores de pip
   double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
   int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
   double pipValue = point;
   if(digits == 5 || digits == 3) pipValue = point * 10;

   // Calcular novo stop loss para acompanhar o preço (trailing stop)
   double novoStop = 0;
   if(tipo == POSITION_TYPE_BUY)
   {
      // Para compras, mover o stop para trailingStep pips abaixo do preço atual
      novoStop = precoAtual - (trailingStep * pipValue);
   }
   else
   {
      // Para vendas, mover o stop para trailingStep pips acima do preço atual
      novoStop = precoAtual + (trailingStep * pipValue);
   }

   // Verificar se vale a pena atualizar o stop loss
   bool deveAtualizar = false;
   
   if(tipo == POSITION_TYPE_BUY)
   {
      // Para compra, o novo stop deve ser maior que o atual
      if(stopAtual == 0 || novoStop > stopAtual)
      {
         deveAtualizar = true;
      }
   }
   else
   {
      // Para venda, o novo stop deve ser menor que o atual
      if(stopAtual == 0 || novoStop < stopAtual)
      {
         deveAtualizar = true;
      }
   }

   if(deveAtualizar)
   {
      if(trade.PositionModify(ticket, novoStop, PositionGetDouble(POSITION_TP)))
      {
         // Registrar posição como protegida
         bool posicaoJaRegistrada = false;
         for(int i = 0; i < total_protegidas; i++)
         {
            if(posicoes_protegidas[i].ticket == ticket)
            {
               posicoes_protegidas[i].protegido = true;
               posicoes_protegidas[i].lucroQuandoProtegido = lucroAtual;
               posicaoJaRegistrada = true;
               break;
            }
         }
         
         if(!posicaoJaRegistrada && total_protegidas < 100)
         {
            posicoes_protegidas[total_protegidas].ticket = ticket;
            posicoes_protegidas[total_protegidas].protegido = true;
            posicoes_protegidas[total_protegidas].lucroQuandoProtegido = lucroAtual;
            total_protegidas++;
         }

         string tipoStr = (tipo == POSITION_TYPE_BUY) ? "COMPRA" : "VENDA";
         string mensagem = "🛡️ PROTEÇÃO ATIVADA! Posição #" + IntegerToString(ticket) +
                          " (" + tipoStr + ") - Lucro: $" + DoubleToString(lucroAtual, 2) +
                          " - Stop movido para: " + DoubleToString(novoStop, _Digits) +
                          " (trailing " + IntegerToString(trailingStep) + " pips)";

         Alert(mensagem);
         Print(mensagem);

         // Atualizar gráfico
         AtualizarLinhasSLTP();
      }
      else
      {
         Print("❌ Erro ao aplicar proteção para ticket #", ticket, ": ", GetLastError());
      }
   }
}

//+------------------------------------------------------------------+
void CriarBotoes()
{
   // Botão COMPRA
   ObjectDelete(0, botaoCompra);
   if(ObjectCreate(0, botaoCompra, OBJ_RECTANGLE_LABEL, 0, 0, 0))
   {
      ObjectSetInteger(0, botaoCompra, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
      ObjectSetInteger(0, botaoCompra, OBJPROP_XDISTANCE, 200);
      ObjectSetInteger(0, botaoCompra, OBJPROP_YDISTANCE, 50);
      ObjectSetInteger(0, botaoCompra, OBJPROP_XSIZE, 80);
      ObjectSetInteger(0, botaoCompra, OBJPROP_YSIZE, 30);
      ObjectSetInteger(0, botaoCompra, OBJPROP_BGCOLOR, clrGreen);
      ObjectSetInteger(0, botaoCompra, OBJPROP_BORDER_COLOR, clrWhite);
      ObjectSetInteger(0, botaoCompra, OBJPROP_COLOR, clrWhite);
      ObjectSetString(0, botaoCompra, OBJPROP_TEXT, "COMPRA");
      ObjectSetInteger(0, botaoCompra, OBJPROP_FONTSIZE, 10);
      ObjectSetInteger(0, botaoCompra, OBJPROP_SELECTABLE, true);
   }
   
   // Botão VENDA
   ObjectDelete(0, botaoVenda);
   if(ObjectCreate(0, botaoVenda, OBJ_RECTANGLE_LABEL, 0, 0, 0))
   {
      ObjectSetInteger(0, botaoVenda, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
      ObjectSetInteger(0, botaoVenda, OBJPROP_XDISTANCE, 200);
      ObjectSetInteger(0, botaoVenda, OBJPROP_YDISTANCE, 90);
      ObjectSetInteger(0, botaoVenda, OBJPROP_XSIZE, 80);
      ObjectSetInteger(0, botaoVenda, OBJPROP_YSIZE, 30);
      ObjectSetInteger(0, botaoVenda, OBJPROP_BGCOLOR, clrRed);
      ObjectSetInteger(0, botaoVenda, OBJPROP_BORDER_COLOR, clrWhite);
      ObjectSetInteger(0, botaoVenda, OBJPROP_COLOR, clrWhite);
      ObjectSetString(0, botaoVenda, OBJPROP_TEXT, "VENDA");
      ObjectSetInteger(0, botaoVenda, OBJPROP_FONTSIZE, 10);
      ObjectSetInteger(0, botaoVenda, OBJPROP_SELECTABLE, true);
   }
   
   // Botão FECHAR TUDO
   ObjectDelete(0, botaoFechar);
   if(ObjectCreate(0, botaoFechar, OBJ_RECTANGLE_LABEL, 0, 0, 0))
   {
      ObjectSetInteger(0, botaoFechar, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
      ObjectSetInteger(0, botaoFechar, OBJPROP_XDISTANCE, 155);
      ObjectSetInteger(0, botaoFechar, OBJPROP_YDISTANCE, 130);
      ObjectSetInteger(0, botaoFechar, OBJPROP_XSIZE, 125);
      ObjectSetInteger(0, botaoFechar, OBJPROP_YSIZE, 30);
      ObjectSetInteger(0, botaoFechar, OBJPROP_BGCOLOR, clrDarkOrange);
      ObjectSetInteger(0, botaoFechar, OBJPROP_BORDER_COLOR, clrWhite);
      ObjectSetInteger(0, botaoFechar, OBJPROP_COLOR, clrWhite);
      ObjectSetString(0, botaoFechar, OBJPROP_TEXT, "FECHAR TUDO");
      ObjectSetInteger(0, botaoFechar, OBJPROP_FONTSIZE, 10);
      ObjectSetInteger(0, botaoFechar, OBJPROP_SELECTABLE, true);
   }
}

//+------------------------------------------------------------------+
void CriarTextoInfo()
{
   // Texto de informações
   ObjectDelete(0, textoInfo);
   if(ObjectCreate(0, textoInfo, OBJ_LABEL, 0, 0, 0))
   {
      ObjectSetInteger(0, textoInfo, OBJPROP_CORNER, CORNER_RIGHT_UPPER);
      ObjectSetInteger(0, textoInfo, OBJPROP_XDISTANCE, 200);
      ObjectSetInteger(0, textoInfo, OBJPROP_YDISTANCE, 170);
      ObjectSetInteger(0, textoInfo, OBJPROP_COLOR, clrWhite);
      ObjectSetString(0, textoInfo, OBJPROP_FONT, "Consolas");
      ObjectSetInteger(0, textoInfo, OBJPROP_FONTSIZE, 9);
      ObjectSetInteger(0, textoInfo, OBJPROP_ANCHOR, ANCHOR_RIGHT_UPPER);
      ObjectSetString(0, textoInfo, OBJPROP_TEXT, "Carregando...");
   }
}

//+------------------------------------------------------------------+
void AtualizarTextoInfo()
{
   int totalOrdens = ContarOrdens();
   int totalProtegidas = ContarPosicoesProtegidas();
   double lucroAtual = 0.0;
   double pipsTotal = 0.0;

   // Calcular lucro total e pips de todas as posições
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            lucroAtual += PositionGetDouble(POSITION_PROFIT);

            double point = SymbolInfoDouble(_Symbol, SYMBOL_POINT);
            int digits = (int)SymbolInfoInteger(_Symbol, SYMBOL_DIGITS);
            double pipValue = point;
            if(digits == 5 || digits == 3) pipValue = point * 10;

            double precoAtual = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ?
                               SymbolInfoDouble(_Symbol, SYMBOL_BID) :
                               SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            double precoAbertura = PositionGetDouble(POSITION_PRICE_OPEN);

            double lucroEmPips = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ?
                                (precoAtual - precoAbertura) / pipValue :
                                (precoAbertura - precoAtual) / pipValue;

            pipsTotal += lucroEmPips;
         }
      }
   }

   // Texto formatado com informações relevantes
   string texto = "Ordens: " + IntegerToString(totalOrdens) + 
                  " | Protegidas: " + IntegerToString(totalProtegidas) + 
                  "\nLucro: $" + DoubleToString(lucroAtual, 2) + 
                  " | Pips: " + DoubleToString(pipsTotal, 1) + 
                  "\nGreens: " + IntegerToString(greens) + 
                  " | Reds: " + IntegerToString(reds) + 
                  "\nLucro Dia: $" + DoubleToString(lucroTotal, 2) +
                  "\nLote: " + DoubleToString(lote, 2) + 
                  " | SL: " + IntegerToString(inputStopLoss) + 
                  " | TP: " + IntegerToString(inputTakeProfit) +
                  "\nProt: $" + DoubleToString(lucroProtecao, 2) + 
                  " | Alvo: $" + DoubleToString(lucroFechamentoAutomatico, 2);

   ObjectSetString(0, textoInfo, OBJPROP_TEXT, texto);
}

//+------------------------------------------------------------------+
int ContarPosicoesProtegidas()
{
   int contador = 0;
   for(int i = 0; i < total_protegidas; i++)
   {
      // Verificar se a posição ainda existe
      if(PositionSelectByTicket(posicoes_protegidas[i].ticket))
      {
         contador++;
      }
   }
   return contador;
}

//+------------------------------------------------------------------+
void LimparPosicoesProtegidas()
{
   // Remover posições que já foram fechadas do array de proteção
   for(int i = total_protegidas - 1; i >= 0; i--)
   {
      if(!PositionSelectByTicket(posicoes_protegidas[i].ticket))
      {
         // Posição foi fechada, remover do array
         for(int j = i; j < total_protegidas - 1; j++)
         {
            posicoes_protegidas[j] = posicoes_protegidas[j + 1];
         }
         total_protegidas--;
      }
   }
}

//+------------------------------------------------------------------+
void RemoverTodasLinhasSLTP()
{
   // Remover todas as linhas de SL, TP e entrada
   for(int i = ObjectsTotal(0, 0, OBJ_HLINE) - 1; i >= 0; i--)
   {
      string nome = ObjectName(0, i, 0, OBJ_HLINE);
      if(StringFind(nome, prefixoLinhaSL) == 0 || 
         StringFind(nome, prefixoLinhaTP) == 0 || 
         StringFind(nome, prefixoLinhaEntrada) == 0)
      {
         ObjectDelete(0, nome);
      }
   }
}

//+------------------------------------------------------------------+
void AtualizarLinhasSLTP()
{
   // Primeiro, remover todas as linhas existentes
   RemoverTodasLinhasSLTP();
   
   // Depois, criar novas linhas para cada posição aberta
   for(int i = 0; i < PositionsTotal(); i++)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(PositionSelectByTicket(ticket))
            {
               double stopLoss = PositionGetDouble(POSITION_SL);
               double takeProfit = PositionGetDouble(POSITION_TP);
               double precoEntrada = PositionGetDouble(POSITION_PRICE_OPEN);
               ENUM_POSITION_TYPE tipo = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE);
               
               // Criar linha de preço de entrada
               string nomeLinhaEntrada = prefixoLinhaEntrada + IntegerToString(ticket);
               if(ObjectCreate(0, nomeLinhaEntrada, OBJ_HLINE, 0, 0, precoEntrada))
               {
                  ObjectSetInteger(0, nomeLinhaEntrada, OBJPROP_COLOR, clrYellow);
                  ObjectSetInteger(0, nomeLinhaEntrada, OBJPROP_STYLE, STYLE_SOLID);
                  ObjectSetInteger(0, nomeLinhaEntrada, OBJPROP_WIDTH, 1);
                  ObjectSetString(0, nomeLinhaEntrada, OBJPROP_TOOLTIP, "Entrada #" + IntegerToString(ticket) + 
                                 " (" + (tipo == POSITION_TYPE_BUY ? "COMPRA" : "VENDA") + ")");
               }
               
               // Criar linha de Stop Loss
               if(stopLoss > 0)
               {
                  string nomeLinhaSL = prefixoLinhaSL + IntegerToString(ticket);
                  if(ObjectCreate(0, nomeLinhaSL, OBJ_HLINE, 0, 0, stopLoss))
                  {
                     ObjectSetInteger(0, nomeLinhaSL, OBJPROP_COLOR, clrRed);
                     ObjectSetInteger(0, nomeLinhaSL, OBJPROP_STYLE, STYLE_DASH);
                     ObjectSetInteger(0, nomeLinhaSL, OBJPROP_WIDTH, 1);
                     ObjectSetString(0, nomeLinhaSL, OBJPROP_TOOLTIP, "Stop Loss #" + IntegerToString(ticket) + 
                                    " (" + (tipo == POSITION_TYPE_BUY ? "COMPRA" : "VENDA") + ")");
                  }
               }
               
               // Criar linha de Take Profit
               if(takeProfit > 0)
               {
                  string nomeLinhaTP = prefixoLinhaTP + IntegerToString(ticket);
                  if(ObjectCreate(0, nomeLinhaTP, OBJ_HLINE, 0, 0, takeProfit))
                  {
                     ObjectSetInteger(0, nomeLinhaTP, OBJPROP_COLOR, clrGreen);
                     ObjectSetInteger(0, nomeLinhaTP, OBJPROP_STYLE, STYLE_DASH);
                     ObjectSetInteger(0, nomeLinhaTP, OBJPROP_WIDTH, 1);
                     ObjectSetString(0, nomeLinhaTP, OBJPROP_TOOLTIP, "Take Profit #" + IntegerToString(ticket) + 
                                    " (" + (tipo == POSITION_TYPE_BUY ? "COMPRA" : "VENDA") + ")");
                  }
               }
            }
         }
      }
   }
   
   ChartRedraw();
}

//+------------------------------------------------------------------+
void FecharTodasPosicoes()
{
   int total = PositionsTotal();
   
   // Precisamos iterar de trás para frente porque o PositionsTotal() muda quando fechamos posições
   for(int i = total - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            double lucroAtual = PositionGetDouble(POSITION_PROFIT);
            
            if(trade.PositionClose(ticket))
            {
               Print("✓ Posição fechada manualmente - Ticket #", ticket);
               
               // Atualizar contadores de operações
               if(lucroAtual > 0)
               {
                  greens++;
                  lucroTotal += lucroAtual;
               }
               else if(lucroAtual < 0)
               {
                  reds++;
                  lucroTotal += lucroAtual;
               }
            }
            else
            {
               Print("✗ Erro ao fechar posição: ", GetLastError());
            }
         }
      }
   }
   
   // Atualizar o gráfico após fechar todas as posições
   AtualizarLinhasSLTP();
   AtualizarTextoInfo();
}

//+------------------------------------------------------------------+
void VerificarFechamentoAutomatico()
{
   for(int i = PositionsTotal() - 1; i >= 0; i--)
   {
      if(PositionGetSymbol(i) == _Symbol)
      {
         string comentario = PositionGetString(POSITION_COMMENT);
         if(StringFind(comentario, prefixoComentario) == 0)
         {
            ulong ticket = PositionGetInteger(POSITION_TICKET);
            if(PositionSelectByTicket(ticket))
            {
               double lucroAtual = PositionGetDouble(POSITION_PROFIT);
               
               // Verificar se o lucro atingiu o valor para fechamento automático
               if(lucroAtual >= lucroFechamentoAutomatico)
               {
                  string tipoOrdem = (ENUM_POSITION_TYPE)PositionGetInteger(POSITION_TYPE) == POSITION_TYPE_BUY ? "COMPRA" : "VENDA";
                  string mensagem = "💰 FECHAMENTO AUTOMÁTICO! Posição #" + IntegerToString(ticket) + 
                                   " (" + tipoOrdem + ") atingiu lucro de $" + 
                                   DoubleToString(lucroAtual, 2) + " - Fechando...";
                  
                  Alert(mensagem);
                  Print(mensagem);
                  
                  if(trade.PositionClose(ticket))
                  {
                     Print("✓ Posição fechada por atingir lucro alvo - Ticket #", ticket);
                     
                     // Atualizar contadores
                     greens++;
                     lucroTotal += lucroAtual;
                  }
                  else
                  {
                     Print("✗ Erro ao fechar posição por lucro alvo: ", GetLastError());
                  }
               }
            }
         }
      }
   }
}
